// Content Script
// 负责在网页中注入面板iframe，监听用户交互

console.log('Content script loaded');

let panelIframe = null;
let isPanelVisible = false;
let mouseEdgeTimer = null;
let autocompletePopup = null;
let isAutocompleteVisible = false;
let currentAutocompleteInput = null;
let autocompleteStartPos = 0;

// 监听来自background script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Content script received message:', request);
  
  switch (request.action) {
    case 'toggle-panel':
      togglePanel();
      sendResponse({ success: true });
      break;
      
    case 'hide-panel':
      hidePanel();
      sendResponse({ success: true });
      break;
      
    default:
      console.log('Unknown action:', request.action);
  }
});

// 创建面板iframe
function createPanel() {
  if (panelIframe) {
    return panelIframe;
  }
  
  // 创建iframe容器
  const container = document.createElement('div');
  container.id = 'prompt-manager-container';
  container.style.cssText = `
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    z-index: 2147483647;
    transition: right 0.3s ease-in-out;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    background: white;
    border-left: 1px solid #e0e0e0;
  `;
  
  // 创建iframe
  panelIframe = document.createElement('iframe');
  panelIframe.src = chrome.runtime.getURL('panel.html');
  panelIframe.style.cssText = `
    width: 100%;
    height: 100%;
    border: none;
    background: white;
  `;
  
  container.appendChild(panelIframe);
  document.body.appendChild(container);
  
  // 监听iframe消息
  window.addEventListener('message', handlePanelMessage);
  
  return panelIframe;
}

// 显示面板
function showPanel() {
  if (!panelIframe) {
    createPanel();
  }
  
  const container = document.getElementById('prompt-manager-container');
  if (container) {
    container.style.right = '0px';
    isPanelVisible = true;
    
    // 延迟一点时间确保iframe加载完成后再发送消息
    setTimeout(() => {
      if (panelIframe && panelIframe.contentWindow) {
        panelIframe.contentWindow.postMessage({
          action: 'panel-shown',
          url: window.location.href
        }, '*');
      }
    }, 100);
  }
}

// 隐藏面板
function hidePanel() {
  const container = document.getElementById('prompt-manager-container');
  if (container) {
    container.style.right = '-400px';
    isPanelVisible = false;
  }
}

// 切换面板显示状态
function togglePanel() {
  if (isPanelVisible) {
    hidePanel();
  } else {
    showPanel();
  }
}

// 处理来自面板的消息
function handlePanelMessage(event) {
  // 验证消息来源
  if (event.source !== panelIframe?.contentWindow) {
    return;
  }
  
  console.log('Content script received panel message:', event.data);
  
  switch (event.data.action) {
    case 'insert-prompt':
      insertPromptToActiveInput(event.data.content);
      break;
      
    case 'hide-panel':
      hidePanel();
      break;
      
    case 'get-active-input':
      const activeInput = getActiveInput();
      panelIframe.contentWindow.postMessage({
        action: 'active-input-info',
        hasActiveInput: !!activeInput,
        inputType: activeInput?.tagName?.toLowerCase()
      }, '*');
      break;
      
    default:
      console.log('Unknown panel message action:', event.data.action);
  }
}

// 获取当前活动的输入框
function getActiveInput() {
  const activeElement = document.activeElement;
  
  // 检查是否是输入框
  if (activeElement && (
    activeElement.tagName === 'INPUT' ||
    activeElement.tagName === 'TEXTAREA' ||
    activeElement.contentEditable === 'true'
  )) {
    return activeElement;
  }
  
  // 如果没有焦点输入框，尝试找到最近使用的输入框
  const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="search"], textarea, [contenteditable="true"]');
  
  // 返回第一个可见的输入框
  for (let input of inputs) {
    if (input.offsetParent !== null) { // 检查元素是否可见
      return input;
    }
  }
  
  return null;
}

// 将prompt内容插入到活动输入框
function insertPromptToActiveInput(content) {
  const activeInput = getActiveInput();
  
  if (!activeInput) {
    console.log('No active input found');
    return;
  }
  
  try {
    if (activeInput.contentEditable === 'true') {
      // 处理contenteditable元素
      const selection = window.getSelection();
      const range = selection.getRangeAt(0);
      range.deleteContents();
      range.insertNode(document.createTextNode(content));
      range.collapse(false);
      selection.removeAllRanges();
      selection.addRange(range);
    } else {
      // 处理input和textarea
      const start = activeInput.selectionStart;
      const end = activeInput.selectionEnd;
      const currentValue = activeInput.value;
      
      // 在光标位置插入内容
      const newValue = currentValue.substring(0, start) + content + currentValue.substring(end);
      activeInput.value = newValue;
      
      // 设置光标位置到插入内容的末尾
      const newCursorPos = start + content.length;
      activeInput.setSelectionRange(newCursorPos, newCursorPos);
    }
    
    // 触发input事件，确保页面能检测到内容变化
    activeInput.dispatchEvent(new Event('input', { bubbles: true }));
    activeInput.focus();
    
    console.log('Prompt inserted successfully');
    
    // 插入成功后隐藏面板
    hidePanel();
    
  } catch (error) {
    console.error('Error inserting prompt:', error);
  }
}

// 监听鼠标移动，实现边缘触发
document.addEventListener('mousemove', (event) => {
  const windowWidth = window.innerWidth;
  const mouseX = event.clientX;
  
  // 检查鼠标是否在右侧边缘附近（最后20像素）
  if (mouseX >= windowWidth - 20) {
    if (!mouseEdgeTimer && !isPanelVisible) {
      mouseEdgeTimer = setTimeout(() => {
        showPanel();
        mouseEdgeTimer = null;
      }, 500); // 500ms延迟，避免误触发
    }
  } else {
    // 鼠标离开边缘区域，取消定时器
    if (mouseEdgeTimer) {
      clearTimeout(mouseEdgeTimer);
      mouseEdgeTimer = null;
    }
  }
});

// 监听点击事件，点击面板外部时隐藏面板
document.addEventListener('click', (event) => {
  if (isPanelVisible) {
    const container = document.getElementById('prompt-manager-container');
    if (container && !container.contains(event.target)) {
      hidePanel();
    }
  }

  // 点击自动补全弹窗外部时隐藏
  if (isAutocompleteVisible) {
    if (!autocompletePopup || !autocompletePopup.contains(event.target)) {
      hideAutocomplete();
    }
  }
});

// 监听ESC键，隐藏面板
document.addEventListener('keydown', (event) => {
  if (event.key === 'Escape' && isPanelVisible) {
    hidePanel();
  }
});

// 监听输入事件，检测"#"字符
document.addEventListener('input', handleInputEvent);
document.addEventListener('keydown', handleKeyDownEvent);

function handleInputEvent(event) {
  const target = event.target;

  // 只处理输入框
  if (!isInputElement(target)) {
    return;
  }

  const value = target.value || target.textContent || '';
  const cursorPos = target.selectionStart || 0;

  // 检查是否输入了"#"
  if (value[cursorPos - 1] === '#') {
    // 找到"#"的位置
    const hashPos = cursorPos - 1;
    autocompleteStartPos = hashPos;
    currentAutocompleteInput = target;

    // 显示自动补全
    showAutocomplete(target, '');
  } else if (currentAutocompleteInput === target && isAutocompleteVisible) {
    // 更新自动补全内容
    const hashPos = autocompleteStartPos;
    if (cursorPos > hashPos) {
      const query = value.substring(hashPos + 1, cursorPos);
      updateAutocomplete(query);
    } else {
      // 光标移动到"#"之前，隐藏自动补全
      hideAutocomplete();
    }
  }
}

function handleKeyDownEvent(event) {
  if (isAutocompleteVisible) {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        selectNextAutocompleteItem();
        break;

      case 'ArrowUp':
        event.preventDefault();
        selectPrevAutocompleteItem();
        break;

      case 'Enter':
        event.preventDefault();
        insertSelectedAutocompleteItem();
        break;

      case 'Escape':
        event.preventDefault();
        hideAutocomplete();
        break;

      default:
        break;
    }
  }
}

function isInputElement(element) {
  return element && (
    element.tagName === 'INPUT' ||
    element.tagName === 'TEXTAREA' ||
    element.contentEditable === 'true'
  );
}

// 自动补全相关函数
let autocompletePrompts = [];
let selectedAutocompleteIndex = 0;

async function showAutocomplete(inputElement, query) {
  try {
    // 获取prompts数据
    const response = await chrome.runtime.sendMessage({ action: 'get-prompts' });
    const allPrompts = response || [];

    // 过滤匹配的prompts
    autocompletePrompts = filterPromptsForAutocomplete(allPrompts, query);

    if (autocompletePrompts.length === 0) {
      hideAutocomplete();
      return;
    }

    selectedAutocompleteIndex = 0;

    // 创建或更新自动补全弹窗
    createAutocompletePopup(inputElement);
    updateAutocompleteContent();

    isAutocompleteVisible = true;

  } catch (error) {
    console.error('Error showing autocomplete:', error);
  }
}

function updateAutocomplete(query) {
  if (!isAutocompleteVisible) return;

  // 重新过滤prompts
  chrome.runtime.sendMessage({ action: 'get-prompts' }, (response) => {
    const allPrompts = response || [];
    autocompletePrompts = filterPromptsForAutocomplete(allPrompts, query);

    if (autocompletePrompts.length === 0) {
      hideAutocomplete();
      return;
    }

    selectedAutocompleteIndex = 0;
    updateAutocompleteContent();
  });
}

function filterPromptsForAutocomplete(prompts, query) {
  if (!query) {
    return prompts.slice(0, 5); // 显示前5个
  }

  const lowerQuery = query.toLowerCase();
  return prompts.filter(prompt => {
    return prompt.title.toLowerCase().includes(lowerQuery) ||
           (prompt.description && prompt.description.toLowerCase().includes(lowerQuery));
  }).slice(0, 5); // 最多显示5个
}

function createAutocompletePopup(inputElement) {
  if (autocompletePopup) {
    return;
  }

  autocompletePopup = document.createElement('div');
  autocompletePopup.className = 'prompt-autocomplete-popup';
  autocompletePopup.style.cssText = `
    position: absolute;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    z-index: 2147483646;
    max-width: 300px;
    max-height: 200px;
    overflow-y: auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-size: 14px;
  `;

  // 定位弹窗
  positionAutocompletePopup(inputElement);

  document.body.appendChild(autocompletePopup);
}

function positionAutocompletePopup(inputElement) {
  if (!autocompletePopup) return;

  const rect = inputElement.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

  autocompletePopup.style.left = (rect.left + scrollLeft) + 'px';
  autocompletePopup.style.top = (rect.bottom + scrollTop + 2) + 'px';
}

function updateAutocompleteContent() {
  if (!autocompletePopup) return;

  autocompletePopup.innerHTML = '';

  autocompletePrompts.forEach((prompt, index) => {
    const item = document.createElement('div');
    item.className = `autocomplete-item ${index === selectedAutocompleteIndex ? 'selected' : ''}`;
    item.style.cssText = `
      padding: 8px 12px;
      cursor: pointer;
      border-bottom: 1px solid #f3f4f6;
      ${index === selectedAutocompleteIndex ? 'background: #eff6ff;' : ''}
    `;

    item.innerHTML = `
      <div style="font-weight: 600; color: #111827; margin-bottom: 2px;">${prompt.title}</div>
      ${prompt.description ? `<div style="font-size: 12px; color: #6b7280;">${prompt.description}</div>` : ''}
    `;

    item.addEventListener('click', () => {
      selectedAutocompleteIndex = index;
      insertSelectedAutocompleteItem();
    });

    autocompletePopup.appendChild(item);
  });
}

function selectNextAutocompleteItem() {
  if (autocompletePrompts.length === 0) return;

  selectedAutocompleteIndex = (selectedAutocompleteIndex + 1) % autocompletePrompts.length;
  updateAutocompleteContent();
}

function selectPrevAutocompleteItem() {
  if (autocompletePrompts.length === 0) return;

  selectedAutocompleteIndex = selectedAutocompleteIndex > 0
    ? selectedAutocompleteIndex - 1
    : autocompletePrompts.length - 1;
  updateAutocompleteContent();
}

function insertSelectedAutocompleteItem() {
  if (!currentAutocompleteInput || autocompletePrompts.length === 0) return;

  const selectedPrompt = autocompletePrompts[selectedAutocompleteIndex];
  if (!selectedPrompt) return;

  try {
    const input = currentAutocompleteInput;
    const value = input.value || input.textContent || '';
    const cursorPos = input.selectionStart || value.length;

    // 替换从"#"开始到光标位置的内容
    const beforeHash = value.substring(0, autocompleteStartPos);
    const afterCursor = value.substring(cursorPos);
    const newValue = beforeHash + selectedPrompt.content + afterCursor;

    if (input.contentEditable === 'true') {
      input.textContent = newValue;
    } else {
      input.value = newValue;
    }

    // 设置光标位置
    const newCursorPos = beforeHash.length + selectedPrompt.content.length;
    if (input.setSelectionRange) {
      input.setSelectionRange(newCursorPos, newCursorPos);
    }

    // 触发input事件
    input.dispatchEvent(new Event('input', { bubbles: true }));
    input.focus();

    // 更新使用次数
    updatePromptUsageCount(selectedPrompt.id);

  } catch (error) {
    console.error('Error inserting autocomplete item:', error);
  }

  hideAutocomplete();
}

function hideAutocomplete() {
  if (autocompletePopup) {
    document.body.removeChild(autocompletePopup);
    autocompletePopup = null;
  }

  isAutocompleteVisible = false;
  currentAutocompleteInput = null;
  autocompletePrompts = [];
  selectedAutocompleteIndex = 0;
}

async function updatePromptUsageCount(promptId) {
  try {
    const response = await chrome.runtime.sendMessage({ action: 'get-prompts' });
    const prompts = response || [];
    const prompt = prompts.find(p => p.id === promptId);

    if (prompt) {
      const updatedPrompt = {
        ...prompt,
        usageCount: (prompt.usageCount || 0) + 1,
        lastUsedAt: Date.now()
      };

      await chrome.runtime.sendMessage({
        action: 'save-prompt',
        prompt: updatedPrompt
      });
    }
  } catch (error) {
    console.error('Error updating prompt usage:', error);
  }
}

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
  if (mouseEdgeTimer) {
    clearTimeout(mouseEdgeTimer);
  }
  if (autocompletePopup) {
    document.body.removeChild(autocompletePopup);
  }
});
