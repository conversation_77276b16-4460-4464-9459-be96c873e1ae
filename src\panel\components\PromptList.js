import React, { useEffect, useRef } from 'react';
import PromptItem from './PromptItem';

function PromptList({
  prompts,
  selectedIndex,
  onSelect,
  onInsert,
  getFolderName,
  searchQuery,
  onEdit,
  onDelete
}) {
  const listRef = useRef(null);
  const selectedItemRef = useRef(null);

  // 当选中项改变时，滚动到可见区域
  useEffect(() => {
    if (selectedItemRef.current && listRef.current) {
      const listRect = listRef.current.getBoundingClientRect();
      const itemRect = selectedItemRef.current.getBoundingClientRect();
      
      if (itemRect.top < listRect.top) {
        // 选中项在可视区域上方
        selectedItemRef.current.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start' 
        });
      } else if (itemRect.bottom > listRect.bottom) {
        // 选中项在可视区域下方
        selectedItemRef.current.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'end' 
        });
      }
    }
  }, [selectedIndex]);

  const handleItemClick = (index) => {
    onSelect(index);
  };

  const handleItemDoubleClick = (prompt) => {
    onInsert(prompt);
  };

  if (prompts.length === 0) {
    return (
      <div className="prompt-list empty">
        <div className="empty-state">
          {searchQuery ? (
            <>
              <div className="empty-icon">🔍</div>
              <div className="empty-title">未找到匹配的 Prompt</div>
              <div className="empty-description">
                尝试使用不同的关键词搜索
              </div>
            </>
          ) : (
            <>
              <div className="empty-icon">📝</div>
              <div className="empty-title">暂无 Prompt</div>
              <div className="empty-description">
                点击添加按钮创建您的第一个 Prompt
              </div>
            </>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="prompt-list" ref={listRef}>
      {prompts.map((prompt, index) => (
        <PromptItem
          key={prompt.id}
          ref={index === selectedIndex ? selectedItemRef : null}
          prompt={prompt}
          isSelected={index === selectedIndex}
          folderName={getFolderName(prompt.folderId)}
          searchQuery={searchQuery}
          onClick={() => handleItemClick(index)}
          onDoubleClick={() => handleItemDoubleClick(prompt)}
          onEdit={onEdit}
          onDelete={onDelete}
        />
      ))}
    </div>
  );
}

export default PromptList;
