/* App Component Styles */
.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fff;
  outline: none;
}

/* App Header */
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px 12px;
  border-bottom: 1px solid #e5e5e5;
  background: #fafafa;
}

.app-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.settings-button,
.close-button {
  width: 28px;
  height: 28px;
  border: none;
  background: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.settings-button:hover,
.close-button:hover {
  background: #e5e5e5;
  color: #333;
}

/* Search Bar */
.search-bar {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e5e5;
}

.search-input-container {
  position: relative;
}

.search-input {
  width: 100%;
  padding: 10px 40px 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  pointer-events: none;
}

/* App Content */
.app-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Prompt List */
.prompt-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.prompt-list.empty {
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.empty-description {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
}

/* Prompt Item */
.prompt-item {
  padding: 12px 20px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.prompt-item:hover {
  background-color: #f9fafb;
}

.prompt-item.selected {
  background-color: #eff6ff;
  border-left: 3px solid #3b82f6;
}

.prompt-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.prompt-title {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin: 0;
  flex: 1;
}

.prompt-folder {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
}

.prompt-description {
  font-size: 13px;
  color: #6b7280;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.prompt-item-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.prompt-stats {
  font-size: 12px;
  color: #9ca3af;
}

.usage-count {
  font-size: 11px;
}

.prompt-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.prompt-item:hover .prompt-actions,
.prompt-item.selected .prompt-actions {
  opacity: 1;
}

.action-button {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  transition: all 0.2s ease;
}

.action-button:hover {
  background: #e5e7eb;
  color: #374151;
}

.delete-button:hover {
  background: #fef2f2;
  color: #dc2626;
}

/* Prompt Preview */
.prompt-preview {
  border-top: 1px solid #e5e5e5;
  padding: 16px 20px;
  background: #fafafa;
  max-height: 200px;
  overflow-y: auto;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.preview-title {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.preview-folder {
  font-size: 12px;
  color: #6b7280;
  background: #e5e7eb;
  padding: 2px 6px;
  border-radius: 10px;
}

.preview-description {
  margin-bottom: 12px;
}

.preview-description p {
  font-size: 13px;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.preview-content {
  margin-bottom: 12px;
}

.content-label {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 6px 0;
}

.content-text {
  font-size: 12px;
  color: #4b5563;
  background: #fff;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
  white-space: pre-wrap;
  max-height: 80px;
  overflow-y: auto;
  line-height: 1.4;
}

.preview-meta {
  border-top: 1px solid #e5e7eb;
  padding-top: 8px;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 11px;
}

.meta-label {
  color: #6b7280;
}

.meta-value {
  color: #374151;
  font-weight: 500;
}

/* App Footer */
.app-footer {
  border-top: 1px solid #e5e5e5;
  padding: 8px 20px;
  background: #fafafa;
}

.shortcuts-hint {
  display: flex;
  gap: 16px;
  font-size: 11px;
  color: #6b7280;
}

.shortcuts-hint span {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Settings Panel */
.settings-panel {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #e5e5e5;
  background: #fafafa;
}

.settings-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.settings-content {
  padding: 20px;
  max-height: calc(80vh - 80px);
  overflow-y: auto;
}

.settings-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #f3f4f6;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
  margin-right: 16px;
}

.setting-info h4 {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
}

.setting-info p {
  font-size: 13px;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.setting-button {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
}

.setting-button:hover:not(.disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.setting-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.export-button:hover:not(.disabled) {
  background: #eff6ff;
  border-color: #3b82f6;
  color: #3b82f6;
}

.import-controls {
  display: flex;
  align-items: center;
}

.file-input {
  display: none;
}

.import-button:hover:not(.disabled) {
  background: #f0fdf4;
  border-color: #22c55e;
  color: #22c55e;
}

/* Message */
.message {
  margin-top: 16px;
  padding: 12px 16px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
}

.message.success {
  background: #f0fdf4;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.message.error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.message-close {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  margin-left: 8px;
  opacity: 0.7;
}

.message-close:hover {
  opacity: 1;
}
