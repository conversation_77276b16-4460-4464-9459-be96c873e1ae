# 优雅 Prompt 管理器 Chrome 插件

一款优雅高效的 Prompt 管理 Chrome 插件，帮助您轻松管理和快速使用各种 Prompt 模板。

## 功能特性

### 🚀 快速唤出
- **快捷键唤出**: 使用 `Alt+S` 快捷键快速打开面板
- **鼠标边缘触发**: 鼠标移动到浏览器右侧边缘自动显示面板
- **固定右侧面板**: 面板固定在浏览器右侧，不干扰正常浏览

### 🔍 智能搜索
- **实时搜索**: 输入关键词即时过滤 Prompt 列表
- **全文搜索**: 支持搜索标题、描述、内容和文件夹名称
- **关键词高亮**: 搜索结果中高亮显示匹配的关键词
- **文件夹路径提示**: 显示每个 Prompt 所属的文件夹路径

### ⌨️ 键盘导航
- **方向键导航**: 使用 ↑↓ 方向键在 Prompt 列表中导航
- **快速插入**: 按 `Enter` 键将选中的 Prompt 插入到当前输入框
- **ESC 关闭**: 按 `Esc` 键快速关闭面板

### 📝 Prompt 上屏
- **智能识别输入框**: 自动识别当前活动的输入框
- **多种输入框支持**: 支持 input、textarea 和 contenteditable 元素
- **光标位置插入**: 在光标当前位置插入 Prompt 内容
- **自动焦点管理**: 插入后自动将焦点返回到输入框

### ⚡ "#" 快捷输入
- **快捷触发**: 在任意输入框中输入 "#" 字符触发自动补全
- **实时补全**: 根据 "#" 后的关键词实时显示匹配的 Prompt
- **键盘选择**: 使用方向键选择补全项，Enter 确认插入
- **智能替换**: 自动替换从 "#" 开始到光标位置的内容

### 📁 Prompt 管理
- **添加 Prompt**: 支持添加新的 Prompt，包含标题、描述、内容和文件夹
- **编辑 Prompt**: 点击编辑按钮修改现有 Prompt
- **删除 Prompt**: 支持删除不需要的 Prompt（需二次确认）
- **文件夹分类**: 支持将 Prompt 分类到不同文件夹中
- **使用统计**: 自动记录每个 Prompt 的使用次数和最后使用时间

### 💾 数据管理
- **数据导出**: 将所有 Prompt 和文件夹导出为 JSON 文件
- **数据导入**: 从 JSON 文件导入 Prompt 和文件夹数据
- **本地存储**: 所有数据存储在本地，保护隐私安全
- **数据验证**: 导入时自动验证数据格式和完整性

## 安装方法

### 开发版安装
1. 下载或克隆此项目到本地
2. 运行 `npm install` 安装依赖
3. 运行 `npm run build` 构建项目
4. 打开 Chrome 浏览器，进入 `chrome://extensions/`
5. 开启"开发者模式"
6. 点击"加载已解压的扩展程序"
7. 选择项目的 `dist` 文件夹

### Chrome Web Store 安装
（待发布到 Chrome Web Store）

## 使用方法

### 基本使用
1. 安装插件后，使用 `Alt+S` 快捷键或将鼠标移动到浏览器右侧边缘唤出面板
2. 在搜索框中输入关键词搜索 Prompt
3. 使用方向键选择需要的 Prompt
4. 按 `Enter` 键将 Prompt 插入到当前输入框

### 快捷输入
1. 在任意网页的输入框中输入 "#" 字符
2. 继续输入关键词，系统会自动显示匹配的 Prompt
3. 使用方向键选择需要的 Prompt
4. 按 `Enter` 键确认插入

### 管理 Prompt
1. 点击面板右上角的设置按钮打开设置面板
2. 使用导入/导出功能管理数据
3. 点击"添加 Prompt"按钮创建新的 Prompt
4. 点击 Prompt 项右侧的编辑/删除按钮进行管理

## 快捷键

- `Alt+S`: 切换面板显示/隐藏
- `↑/↓`: 在 Prompt 列表中导航
- `Enter`: 插入选中的 Prompt
- `Esc`: 关闭面板或取消操作
- `Ctrl+Enter`: 在编辑器中保存 Prompt

## 技术栈

- **前端框架**: React 18
- **构建工具**: Webpack 5
- **样式**: CSS3
- **存储**: Chrome Storage API
- **架构**: Manifest V3

## 开发

### 环境要求
- Node.js 16+
- npm 或 yarn

### 开发命令
```bash
# 安装依赖
npm install

# 开发模式构建（带监听）
npm run dev

# 生产模式构建
npm run build

# 清理构建文件
npm run clean
```

### 项目结构
```
src/
├── background/     # Background Script
├── content/        # Content Script
├── panel/          # React 面板应用
│   ├── components/ # React 组件
│   ├── App.js      # 主应用组件
│   └── index.js    # 入口文件
├── common/         # 通用工具函数
public/
├── manifest.json   # 插件清单文件
├── panel.html      # 面板 HTML
└── icons/          # 插件图标
```

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 许可证

MIT License

## 更新日志

### v0.1.0
- 初始版本发布
- 实现基本的 Prompt 管理功能
- 支持快捷键和鼠标边缘触发
- 实现智能搜索和键盘导航
- 支持 "#" 快捷输入功能
- 实现数据导入导出功能
