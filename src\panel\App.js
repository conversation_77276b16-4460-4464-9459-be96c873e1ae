import React, { useState, useEffect, useRef } from 'react';
import SearchBar from './components/SearchBar';
import PromptList from './components/PromptList';
import PromptPreview from './components/PromptPreview';
import SettingsPanel from './components/SettingsPanel';
import PromptEditor from './components/PromptEditor';
import './App.css';

function App() {
  const [prompts, setPrompts] = useState([]);
  const [folders, setFolders] = useState([]);
  const [filteredPrompts, setFilteredPrompts] = useState([]);
  const [selectedPromptIndex, setSelectedPromptIndex] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  const [showEditor, setShowEditor] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState(null);
  const searchInputRef = useRef(null);

  // 初始化数据
  useEffect(() => {
    loadData();
    
    // 监听来自content script的消息
    window.addEventListener('message', handleContentMessage);
    
    return () => {
      window.removeEventListener('message', handleContentMessage);
    };
  }, []);

  // 当面板显示时，自动聚焦搜索框
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  // 处理搜索
  useEffect(() => {
    filterPrompts(searchQuery);
  }, [prompts, searchQuery]);

  // 重置选中索引当过滤结果改变时
  useEffect(() => {
    setSelectedPromptIndex(0);
  }, [filteredPrompts]);

  // 加载数据
  const loadData = async () => {
    try {
      setLoading(true);
      
      // 获取prompts
      const promptsResponse = await sendMessageToBackground({ action: 'get-prompts' });
      const foldersResponse = await sendMessageToBackground({ action: 'get-folders' });
      
      setPrompts(promptsResponse || []);
      setFolders(foldersResponse || []);
      
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  // 向background script发送消息
  const sendMessageToBackground = (message) => {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    });
  };

  // 处理来自content script的消息
  const handleContentMessage = (event) => {
    console.log('Panel received message:', event.data);
    
    switch (event.data.action) {
      case 'panel-shown':
        // 面板显示时，聚焦搜索框
        if (searchInputRef.current) {
          setTimeout(() => {
            searchInputRef.current.focus();
          }, 100);
        }
        break;
        
      default:
        break;
    }
  };

  // 过滤prompts
  const filterPrompts = (query) => {
    if (!query.trim()) {
      setFilteredPrompts(prompts);
      return;
    }

    const lowerQuery = query.toLowerCase();
    const filtered = prompts.filter(prompt => {
      // 搜索标题、描述、内容
      const titleMatch = prompt.title.toLowerCase().includes(lowerQuery);
      const descriptionMatch = prompt.description?.toLowerCase().includes(lowerQuery);
      const contentMatch = prompt.content.toLowerCase().includes(lowerQuery);
      
      // 搜索文件夹名称
      let folderMatch = false;
      if (prompt.folderId) {
        const folder = folders.find(f => f.id === prompt.folderId);
        if (folder) {
          folderMatch = folder.name.toLowerCase().includes(lowerQuery);
        }
      }
      
      return titleMatch || descriptionMatch || contentMatch || folderMatch;
    });

    setFilteredPrompts(filtered);
  };

  // 处理键盘导航
  const handleKeyDown = (event) => {
    if (filteredPrompts.length === 0) return;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setSelectedPromptIndex(prev => 
          prev < filteredPrompts.length - 1 ? prev + 1 : 0
        );
        break;
        
      case 'ArrowUp':
        event.preventDefault();
        setSelectedPromptIndex(prev => 
          prev > 0 ? prev - 1 : filteredPrompts.length - 1
        );
        break;
        
      case 'Enter':
        event.preventDefault();
        if (filteredPrompts[selectedPromptIndex]) {
          insertPrompt(filteredPrompts[selectedPromptIndex]);
        }
        break;
        
      case 'Escape':
        event.preventDefault();
        hidePanel();
        break;
        
      default:
        break;
    }
  };

  // 插入prompt到页面
  const insertPrompt = (prompt) => {
    console.log('Inserting prompt:', prompt);
    
    // 向content script发送消息
    window.parent.postMessage({
      action: 'insert-prompt',
      content: prompt.content
    }, '*');
    
    // 更新使用次数
    updatePromptUsage(prompt.id);
  };

  // 更新prompt使用次数
  const updatePromptUsage = async (promptId) => {
    try {
      const prompt = prompts.find(p => p.id === promptId);
      if (prompt) {
        const updatedPrompt = {
          ...prompt,
          usageCount: (prompt.usageCount || 0) + 1,
          lastUsedAt: Date.now()
        };
        
        await sendMessageToBackground({
          action: 'save-prompt',
          prompt: updatedPrompt
        });
        
        // 更新本地状态
        setPrompts(prev => prev.map(p => 
          p.id === promptId ? updatedPrompt : p
        ));
      }
    } catch (error) {
      console.error('Error updating prompt usage:', error);
    }
  };

  // 隐藏面板
  const hidePanel = () => {
    window.parent.postMessage({
      action: 'hide-panel'
    }, '*');
  };

  // 获取文件夹名称
  const getFolderName = (folderId) => {
    if (!folderId) return null;
    const folder = folders.find(f => f.id === folderId);
    return folder ? folder.name : null;
  };

  // 处理数据导入后的刷新
  const handleDataImported = () => {
    loadData();
  };

  // 显示设置面板
  const showSettingsPanel = () => {
    setShowSettings(true);
  };

  // 隐藏设置面板
  const hideSettingsPanel = () => {
    setShowSettings(false);
  };

  // 显示添加Prompt编辑器
  const showAddPromptEditor = () => {
    setEditingPrompt(null);
    setShowEditor(true);
  };

  // 显示编辑Prompt编辑器
  const showEditPromptEditor = (prompt) => {
    setEditingPrompt(prompt);
    setShowEditor(true);
  };

  // 隐藏编辑器
  const hideEditor = () => {
    setShowEditor(false);
    setEditingPrompt(null);
  };

  // 保存Prompt
  const savePrompt = async (promptData) => {
    try {
      const response = await sendMessageToBackground({
        action: 'save-prompt',
        prompt: promptData
      });

      if (response.success) {
        // 重新加载数据
        await loadData();
        hideEditor();
      } else {
        throw new Error(response.error || '保存失败');
      }
    } catch (error) {
      console.error('Error saving prompt:', error);
      throw error;
    }
  };

  // 删除Prompt
  const deletePrompt = async (promptId) => {
    try {
      const response = await sendMessageToBackground({
        action: 'delete-prompt',
        promptId
      });

      if (response.success) {
        // 重新加载数据
        await loadData();
        hideEditor();
      } else {
        throw new Error(response.error || '删除失败');
      }
    } catch (error) {
      console.error('Error deleting prompt:', error);
      throw error;
    }
  };

  if (loading) {
    return (
      <div className="app loading">
        <div className="loading-spinner">加载中...</div>
      </div>
    );
  }

  return (
    <div className="app" onKeyDown={handleKeyDown} tabIndex={0}>
      <div className="app-header">
        <h1 className="app-title">Prompt 管理器</h1>
        <div className="header-actions">
          <button className="settings-button" onClick={showSettingsPanel} title="设置">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="3"></circle>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
            </svg>
          </button>
          <button className="close-button" onClick={hidePanel}>×</button>
        </div>
      </div>
      
      <SearchBar
        ref={searchInputRef}
        value={searchQuery}
        onChange={setSearchQuery}
        placeholder="搜索 Prompt..."
      />
      
      <div className="app-content">
        <div className="content-header">
          <button
            className="add-prompt-button"
            onClick={showAddPromptEditor}
            title="添加新的 Prompt"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            添加 Prompt
          </button>
        </div>

        <PromptList
          prompts={filteredPrompts}
          selectedIndex={selectedPromptIndex}
          onSelect={setSelectedPromptIndex}
          onInsert={insertPrompt}
          getFolderName={getFolderName}
          searchQuery={searchQuery}
          onEdit={showEditPromptEditor}
          onDelete={deletePrompt}
        />
        
        {filteredPrompts[selectedPromptIndex] && (
          <PromptPreview
            prompt={filteredPrompts[selectedPromptIndex]}
            folderName={getFolderName(filteredPrompts[selectedPromptIndex].folderId)}
          />
        )}
      </div>
      
      <div className="app-footer">
        <div className="shortcuts-hint">
          <span>↑↓ 选择</span>
          <span>Enter 插入</span>
          <span>Esc 关闭</span>
        </div>
      </div>

      {showSettings && (
        <div className="modal-overlay">
          <SettingsPanel
            onClose={hideSettingsPanel}
            onDataImported={handleDataImported}
          />
        </div>
      )}

      {showEditor && (
        <div className="modal-overlay">
          <PromptEditor
            prompt={editingPrompt}
            folders={folders}
            onSave={savePrompt}
            onCancel={hideEditor}
            onDelete={deletePrompt}
          />
        </div>
      )}
    </div>
  );
}

export default App;
