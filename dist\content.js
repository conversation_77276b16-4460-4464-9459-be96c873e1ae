(()=>{console.log("Content script loaded");let e=null,t=!1,n=null;function o(){e||function(){if(e)return e;const t=document.createElement("div");t.id="prompt-manager-container",t.style.cssText="\n    position: fixed;\n    top: 0;\n    right: -400px;\n    width: 400px;\n    height: 100vh;\n    z-index: 2147483647;\n    transition: right 0.3s ease-in-out;\n    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);\n    background: white;\n    border-left: 1px solid #e0e0e0;\n  ",e=document.createElement("iframe"),e.src=chrome.runtime.getURL("panel.html"),e.style.cssText="\n    width: 100%;\n    height: 100%;\n    border: none;\n    background: white;\n  ",t.appendChild(e),document.body.appendChild(t),window.addEventListener("message",a)}();const n=document.getElementById("prompt-manager-container");n&&(n.style.right="0px",t=!0,setTimeout(()=>{e&&e.contentWindow&&e.contentWindow.postMessage({action:"panel-shown",url:window.location.href},"*")},100))}function i(){const e=document.getElementById("prompt-manager-container");e&&(e.style.right="-400px",t=!1)}function a(t){var n,o;if(t.source===(null===(n=e)||void 0===n?void 0:n.contentWindow))switch(console.log("Content script received panel message:",t.data),t.data.action){case"insert-prompt":!function(e){const t=c();if(t)try{if("true"===t.contentEditable){const t=window.getSelection(),n=t.getRangeAt(0);n.deleteContents(),n.insertNode(document.createTextNode(e)),n.collapse(!1),t.removeAllRanges(),t.addRange(n)}else{const n=t.selectionStart,o=t.selectionEnd,i=t.value,a=i.substring(0,n)+e+i.substring(o);t.value=a;const c=n+e.length;t.setSelectionRange(c,c)}t.dispatchEvent(new Event("input",{bubbles:!0})),t.focus(),console.log("Prompt inserted successfully"),i()}catch(e){console.error("Error inserting prompt:",e)}else console.log("No active input found")}(t.data.content);break;case"hide-panel":i();break;case"get-active-input":const n=c();e.contentWindow.postMessage({action:"active-input-info",hasActiveInput:!!n,inputType:null==n||null===(o=n.tagName)||void 0===o?void 0:o.toLowerCase()},"*");break;default:console.log("Unknown panel message action:",t.data.action)}}function c(){const e=document.activeElement;if(e&&("INPUT"===e.tagName||"TEXTAREA"===e.tagName||"true"===e.contentEditable))return e;const t=document.querySelectorAll('input[type="text"], input[type="email"], input[type="search"], textarea, [contenteditable="true"]');for(let e of t)if(null!==e.offsetParent)return e;return null}chrome.runtime.onMessage.addListener((e,n,a)=>{switch(console.log("Content script received message:",e),e.action){case"toggle-panel":t?i():o(),a({success:!0});break;case"hide-panel":i(),a({success:!0});break;default:console.log("Unknown action:",e.action)}}),document.addEventListener("mousemove",e=>{const i=window.innerWidth;e.clientX>=i-20?n||t||(n=setTimeout(()=>{o(),n=null},500)):n&&(clearTimeout(n),n=null)}),document.addEventListener("click",e=>{if(t){const t=document.getElementById("prompt-manager-container");t&&!t.contains(e.target)&&i()}}),document.addEventListener("keydown",e=>{"Escape"===e.key&&t&&i()}),window.addEventListener("beforeunload",()=>{n&&clearTimeout(n)})})();