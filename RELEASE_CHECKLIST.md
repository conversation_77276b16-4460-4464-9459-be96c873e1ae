# 发布前检查清单

## 代码质量检查

- [x] 移除所有 console.log 调试语句
- [x] 代码格式化和规范检查
- [x] 错误处理完善
- [x] 性能优化

## 功能测试

### 基础功能
- [ ] 插件安装和卸载
- [ ] 快捷键 Alt+S 唤出面板
- [ ] 鼠标边缘触发面板
- [ ] 面板显示和隐藏动画

### 搜索功能
- [ ] 实时搜索过滤
- [ ] 关键词高亮显示
- [ ] 搜索范围（标题、描述、内容、文件夹）
- [ ] 空搜索结果处理

### 键盘导航
- [ ] 方向键上下导航
- [ ] Enter 键插入 Prompt
- [ ] Esc 键关闭面板
- [ ] 自动滚动到选中项

### Prompt 上屏
- [ ] 识别不同类型输入框（input、textarea、contenteditable）
- [ ] 光标位置正确插入
- [ ] 插入后焦点管理
- [ ] 面板自动隐藏

### "#" 快捷输入
- [ ] "#" 字符触发自动补全
- [ ] 实时过滤补全列表
- [ ] 键盘选择补全项
- [ ] Enter 确认插入
- [ ] 点击外部隐藏补全

### Prompt 管理
- [ ] 添加新 Prompt
- [ ] 编辑现有 Prompt
- [ ] 删除 Prompt（二次确认）
- [ ] 表单验证
- [ ] 文件夹选择

### 数据管理
- [ ] 数据导出功能
- [ ] 数据导入功能
- [ ] 数据格式验证
- [ ] 错误处理和提示

## 兼容性测试

### 浏览器兼容性
- [ ] Chrome 最新版本
- [ ] Chrome 88+ 版本
- [ ] Edge（Chromium 内核）

### 网站兼容性
- [ ] Gmail
- [ ] 微信网页版
- [ ] 知乎
- [ ] GitHub
- [ ] 百度
- [ ] 其他常用网站

### 输入框兼容性
- [ ] 普通 input 输入框
- [ ] textarea 文本域
- [ ] contenteditable 富文本编辑器
- [ ] 第三方编辑器（如 CodeMirror）

## 性能测试

- [ ] 插件启动时间
- [ ] 面板打开速度
- [ ] 搜索响应时间
- [ ] 大量 Prompt 时的性能
- [ ] 内存使用情况

## 安全检查

- [ ] 内容安全策略（CSP）合规
- [ ] 权限最小化原则
- [ ] 用户数据隐私保护
- [ ] XSS 防护

## 用户体验

- [ ] 界面美观度
- [ ] 交互流畅性
- [ ] 错误提示友好性
- [ ] 快捷键提示
- [ ] 帮助文档完整性

## 文档完善

- [ ] README.md 完整性
- [ ] 安装说明清晰
- [ ] 使用方法详细
- [ ] 常见问题解答
- [ ] 更新日志

## 发布准备

### Chrome Web Store
- [ ] 应用描述
- [ ] 应用截图（至少1张，最多5张）
- [ ] 应用图标（128x128）
- [ ] 隐私政策
- [ ] 分类选择
- [ ] 关键词设置

### 版本管理
- [ ] 版本号更新
- [ ] 更新日志编写
- [ ] Git 标签创建
- [ ] 发布包打包

## 发布后监控

- [ ] 用户反馈收集
- [ ] 错误日志监控
- [ ] 性能指标跟踪
- [ ] 用户使用数据分析

## 已知问题

### 当前版本限制
- 文件夹管理功能未完全实现
- 拖拽排序功能未实现
- 暂无云同步功能

### 计划改进
- 添加更多快捷键自定义选项
- 实现 Prompt 模板化功能
- 添加使用统计和智能推荐
- 支持 Prompt 分享功能

## 测试环境

- 操作系统：Windows 10/11, macOS, Linux
- 浏览器：Chrome 88+, Edge 88+
- 屏幕分辨率：1920x1080, 1366x768, 2560x1440
- 网络环境：正常网络、慢速网络

## 发布时间表

1. **内部测试**：1-2 天
2. **用户测试**：3-5 天
3. **问题修复**：1-2 天
4. **最终检查**：1 天
5. **正式发布**：提交到 Chrome Web Store

## 联系信息

- 开发者邮箱：[待填写]
- 项目地址：[待填写]
- 问题反馈：[待填写]
