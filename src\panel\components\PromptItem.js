import React, { forwardRef } from 'react';

const PromptItem = forwardRef(({
  prompt,
  isSelected,
  folderName,
  searchQuery,
  onClick,
  onDoubleClick,
  onEdit,
  onDelete
}, ref) => {
  
  // 高亮搜索关键词
  const highlightText = (text, query) => {
    if (!query || !text) return text;
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="highlight">{part}</mark>
      ) : part
    );
  };

  const handleClick = () => {
    onClick();
  };

  const handleDoubleClick = () => {
    onDoubleClick();
  };

  return (
    <div
      ref={ref}
      className={`prompt-item ${isSelected ? 'selected' : ''}`}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
    >
      <div className="prompt-item-header">
        <h3 className="prompt-title">
          {highlightText(prompt.title, searchQuery)}
        </h3>
        {folderName && (
          <span className="prompt-folder">
            {highlightText(folderName, searchQuery)}
          </span>
        )}
      </div>
      
      {prompt.description && (
        <p className="prompt-description">
          {highlightText(prompt.description, searchQuery)}
        </p>
      )}
      
      <div className="prompt-item-footer">
        <div className="prompt-stats">
          {prompt.usageCount > 0 && (
            <span className="usage-count">
              使用 {prompt.usageCount} 次
            </span>
          )}
        </div>
        <div className="prompt-actions">
          <button
            className="action-button edit-button"
            onClick={(e) => {
              e.stopPropagation();
              if (onEdit) {
                onEdit(prompt);
              }
            }}
            title="编辑"
          >
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
            </svg>
          </button>
          <button
            className="action-button delete-button"
            onClick={(e) => {
              e.stopPropagation();
              if (onDelete && window.confirm('确定要删除这个 Prompt 吗？')) {
                onDelete(prompt.id);
              }
            }}
            title="删除"
          >
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="3,6 5,6 21,6"></polyline>
              <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"></path>
              <line x1="10" y1="11" x2="10" y2="17"></line>
              <line x1="14" y1="11" x2="14" y2="17"></line>
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
});

PromptItem.displayName = 'PromptItem';

export default PromptItem;
