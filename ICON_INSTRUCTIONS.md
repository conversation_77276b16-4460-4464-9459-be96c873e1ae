# 图标制作说明

## 图标设计

项目根目录下的 `icon-design.svg` 文件包含了插件图标的设计。

## 图标规格

Chrome 插件需要以下尺寸的图标：
- 16x16 像素 (工具栏图标)
- 48x48 像素 (扩展管理页面)
- 128x128 像素 (Chrome Web Store)

## 制作步骤

1. 使用任何支持 SVG 的图像编辑软件（如 Inkscape、Adobe Illustrator、Figma 等）打开 `icon-design.svg`
2. 将 SVG 导出为以下尺寸的 PNG 文件：
   - `public/icons/icon16.png` (16x16)
   - `public/icons/icon48.png` (48x48)
   - `public/icons/icon128.png` (128x128)

## 在线工具

如果没有图像编辑软件，可以使用以下在线工具：

1. **Canva**: https://www.canva.com/
   - 上传 SVG 文件
   - 调整尺寸并导出为 PNG

2. **SVGOMG**: https://jakearchibald.github.io/svgomg/
   - 优化 SVG 文件

3. **CloudConvert**: https://cloudconvert.com/svg-to-png
   - 在线 SVG 转 PNG 工具

## 设计说明

图标设计包含以下元素：
- 蓝色圆形背景：代表专业和可信赖
- 白色文档图标：代表 Prompt 内容管理
- "#" 符号：代表快捷输入功能
- 装饰性光点：增加视觉吸引力

## 临时解决方案

在正式图标制作完成之前，当前的空白 PNG 文件可以正常使用，只是在浏览器中不会显示图标。

## 注意事项

- 确保图标在不同背景下都清晰可见
- 保持设计简洁，避免过多细节
- 确保 16x16 尺寸下仍然可识别
