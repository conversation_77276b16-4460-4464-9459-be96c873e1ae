import React, { useState, useEffect } from 'react';

function PromptEditor({ 
  prompt, 
  folders, 
  onSave, 
  onCancel, 
  onDelete 
}) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    folderId: ''
  });
  const [errors, setErrors] = useState({});
  const [saving, setSaving] = useState(false);
  const [deleting, setDeleting] = useState(false);

  const isEditing = !!prompt;

  useEffect(() => {
    if (prompt) {
      setFormData({
        title: prompt.title || '',
        description: prompt.description || '',
        content: prompt.content || '',
        folderId: prompt.folderId || ''
      });
    } else {
      setFormData({
        title: '',
        description: '',
        content: '',
        folderId: ''
      });
    }
    setErrors({});
  }, [prompt]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.title.trim()) {
      newErrors.title = '标题不能为空';
    } else if (formData.title.length > 100) {
      newErrors.title = '标题长度不能超过100个字符';
    }
    
    if (!formData.content.trim()) {
      newErrors.content = '内容不能为空';
    }
    
    if (formData.description && formData.description.length > 200) {
      newErrors.description = '描述长度不能超过200个字符';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }
    
    setSaving(true);
    
    try {
      const promptData = {
        ...formData,
        title: formData.title.trim(),
        description: formData.description.trim(),
        content: formData.content.trim(),
        folderId: formData.folderId || null
      };
      
      if (isEditing) {
        promptData.id = prompt.id;
      }
      
      await onSave(promptData);
    } catch (error) {
      console.error('Error saving prompt:', error);
      setErrors({ general: '保存失败，请重试' });
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!isEditing || !prompt) return;
    
    if (!window.confirm('确定要删除这个 Prompt 吗？此操作不可撤销。')) {
      return;
    }
    
    setDeleting(true);
    
    try {
      await onDelete(prompt.id);
    } catch (error) {
      console.error('Error deleting prompt:', error);
      setErrors({ general: '删除失败，请重试' });
    } finally {
      setDeleting(false);
    }
  };

  const handleKeyDown = (event) => {
    if (event.key === 'Escape') {
      onCancel();
    } else if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      handleSave();
    }
  };

  return (
    <div className="prompt-editor" onKeyDown={handleKeyDown}>
      <div className="editor-header">
        <h2 className="editor-title">
          {isEditing ? '编辑 Prompt' : '添加 Prompt'}
        </h2>
        <button className="close-button" onClick={onCancel}>×</button>
      </div>
      
      <div className="editor-content">
        {errors.general && (
          <div className="error-message general-error">
            {errors.general}
          </div>
        )}
        
        <div className="form-group">
          <label className="form-label" htmlFor="prompt-title">
            标题 <span className="required">*</span>
          </label>
          <input
            id="prompt-title"
            type="text"
            className={`form-input ${errors.title ? 'error' : ''}`}
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            placeholder="输入 Prompt 标题"
            maxLength={100}
          />
          {errors.title && (
            <div className="error-message">{errors.title}</div>
          )}
        </div>
        
        <div className="form-group">
          <label className="form-label" htmlFor="prompt-description">
            描述
          </label>
          <input
            id="prompt-description"
            type="text"
            className={`form-input ${errors.description ? 'error' : ''}`}
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="输入 Prompt 描述（可选）"
            maxLength={200}
          />
          {errors.description && (
            <div className="error-message">{errors.description}</div>
          )}
        </div>
        
        <div className="form-group">
          <label className="form-label" htmlFor="prompt-folder">
            文件夹
          </label>
          <select
            id="prompt-folder"
            className="form-select"
            value={formData.folderId}
            onChange={(e) => handleInputChange('folderId', e.target.value)}
          >
            <option value="">无文件夹</option>
            {folders.map(folder => (
              <option key={folder.id} value={folder.id}>
                {folder.name}
              </option>
            ))}
          </select>
        </div>
        
        <div className="form-group">
          <label className="form-label" htmlFor="prompt-content">
            内容 <span className="required">*</span>
          </label>
          <textarea
            id="prompt-content"
            className={`form-textarea ${errors.content ? 'error' : ''}`}
            value={formData.content}
            onChange={(e) => handleInputChange('content', e.target.value)}
            placeholder="输入 Prompt 内容"
            rows={8}
          />
          {errors.content && (
            <div className="error-message">{errors.content}</div>
          )}
        </div>
      </div>
      
      <div className="editor-footer">
        <div className="footer-left">
          {isEditing && (
            <button
              className="delete-button"
              onClick={handleDelete}
              disabled={deleting}
            >
              {deleting ? '删除中...' : '删除'}
            </button>
          )}
        </div>
        
        <div className="footer-right">
          <button
            className="cancel-button"
            onClick={onCancel}
            disabled={saving || deleting}
          >
            取消
          </button>
          <button
            className="save-button"
            onClick={handleSave}
            disabled={saving || deleting}
          >
            {saving ? '保存中...' : '保存'}
          </button>
        </div>
      </div>
      
      <div className="editor-shortcuts">
        <span>Ctrl+Enter 保存</span>
        <span>Esc 取消</span>
      </div>
    </div>
  );
}

export default PromptEditor;
