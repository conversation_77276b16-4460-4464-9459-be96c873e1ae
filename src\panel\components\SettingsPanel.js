import React, { useState } from 'react';

function SettingsPanel({ onClose, onDataImported }) {
  const [importing, setImporting] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState(''); // 'success' or 'error'

  // 导出数据
  const handleExport = async () => {
    try {
      setExporting(true);
      setMessage('');
      
      const response = await sendMessageToBackground({ action: 'export-data' });
      
      if (response.success) {
        // 创建下载链接
        const blob = new Blob([JSON.stringify(response.data, null, 2)], {
          type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `prompt-manager-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        setMessage('数据导出成功！');
        setMessageType('success');
      } else {
        setMessage(`导出失败：${response.error}`);
        setMessageType('error');
      }
    } catch (error) {
      console.error('Export error:', error);
      setMessage(`导出失败：${error.message}`);
      setMessageType('error');
    } finally {
      setExporting(false);
    }
  };

  // 导入数据
  const handleImport = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        setImporting(true);
        setMessage('');
        
        const data = JSON.parse(e.target.result);
        const response = await sendMessageToBackground({ 
          action: 'import-data', 
          data 
        });
        
        if (response.success) {
          setMessage(response.message || '数据导入成功！');
          setMessageType('success');
          
          // 通知父组件数据已更新
          if (onDataImported) {
            onDataImported();
          }
        } else {
          setMessage(`导入失败：${response.error}`);
          setMessageType('error');
        }
      } catch (error) {
        console.error('Import error:', error);
        setMessage(`导入失败：${error.message}`);
        setMessageType('error');
      } finally {
        setImporting(false);
        // 清空文件输入
        event.target.value = '';
      }
    };
    
    reader.readAsText(file);
  };

  // 向background script发送消息
  const sendMessageToBackground = (message) => {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    });
  };

  // 清除消息
  const clearMessage = () => {
    setMessage('');
    setMessageType('');
  };

  return (
    <div className="settings-panel">
      <div className="settings-header">
        <h2 className="settings-title">设置</h2>
        <button className="close-button" onClick={onClose}>×</button>
      </div>
      
      <div className="settings-content">
        <div className="settings-section">
          <h3 className="section-title">数据管理</h3>
          
          <div className="setting-item">
            <div className="setting-info">
              <h4>导出数据</h4>
              <p>将所有 Prompt 和文件夹导出为 JSON 文件</p>
            </div>
            <button 
              className="setting-button export-button"
              onClick={handleExport}
              disabled={exporting}
            >
              {exporting ? '导出中...' : '导出'}
            </button>
          </div>
          
          <div className="setting-item">
            <div className="setting-info">
              <h4>导入数据</h4>
              <p>从 JSON 文件导入 Prompt 和文件夹（会覆盖现有数据）</p>
            </div>
            <div className="import-controls">
              <input
                type="file"
                accept=".json"
                onChange={handleImport}
                disabled={importing}
                className="file-input"
                id="import-file"
              />
              <label 
                htmlFor="import-file" 
                className={`setting-button import-button ${importing ? 'disabled' : ''}`}
              >
                {importing ? '导入中...' : '选择文件'}
              </label>
            </div>
          </div>
        </div>
        
        {message && (
          <div className={`message ${messageType}`}>
            <span>{message}</span>
            <button className="message-close" onClick={clearMessage}>×</button>
          </div>
        )}
      </div>
    </div>
  );
}

export default SettingsPanel;
